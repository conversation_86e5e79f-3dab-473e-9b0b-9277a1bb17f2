import { Button } from "@/components/ui/button";

interface SeoOptimizationHeaderProps {
  totalProducts: number;
  selectedCount: number;
  averageSeoScore: number;
  onOptimizeSelected: () => void;
  onSelectAll: () => void;
  isOptimizing: boolean;
}

export function SeoOptimizationHeader({
  totalProducts,
  selectedCount,
  averageSeoScore,
  onOptimizeSelected,
  onSelectAll,
  isOptimizing
}: SeoOptimizationHeaderProps) {
  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header Section - Following billing/settings pattern */}
      <div className="bg-black py-20 px-6">
        <div className="max-w-6xl mx-auto text-center">
          {/* Logo and Title */}
          <div className="flex flex-col items-center mb-12">
            <img
              src="/logo.png"
              alt="ProdRankX Logo"
              className="w-16 h-16 mb-6 rounded-2xl shadow-2xl"
              style={{ filter: 'brightness(1.1) contrast(1.1)' }}
            />
            <h1 style={{
              fontSize: 'clamp(3rem, 8vw, 6rem)',
              fontWeight: 900,
              lineHeight: 0.9,
              letterSpacing: '-0.05em',
              marginBottom: '1rem',
              color: 'white'
            }}>
              OPTIMIZE
            </h1>
            <p style={{
              fontSize: 'clamp(1.25rem, 3vw, 1.75rem)',
              fontWeight: 300,
              color: '#cbd5e1',
              maxWidth: '40rem',
              margin: '0 auto'
            }}>
              AI-powered SEO optimization for your Shopify products
            </p>
          </div>

          {/* Stats Grid - Following billing/settings pattern */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div className="text-center">
              <div className="text-4xl font-bold mb-2">
                {totalProducts.toLocaleString()}
              </div>
              <div className="text-white/70">Total Products</div>
            </div>

            <div className="text-center">
              <div className="text-4xl font-bold mb-2">
                {selectedCount.toLocaleString()}
              </div>
              <div className="text-white/70">Selected</div>
            </div>

            <div className="text-center">
              <div className="text-4xl font-bold mb-2">
                {Math.round(averageSeoScore)}%
              </div>
              <div className="text-white/70">Avg SEO Score</div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              onClick={onOptimizeSelected}
              disabled={selectedCount === 0 || isOptimizing}
              className="bg-white text-black hover:bg-gray-100 font-bold py-3 px-8 rounded-2xl"
            >
              {isOptimizing ? 'Optimizing...' : `Optimize ${selectedCount} Selected`}
            </Button>

            <Button
              onClick={onSelectAll}
              className="bg-transparent border-2 border-white/40 text-white hover:bg-white hover:text-black font-bold py-3 px-8 rounded-2xl"
            >
              Select All Products
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
